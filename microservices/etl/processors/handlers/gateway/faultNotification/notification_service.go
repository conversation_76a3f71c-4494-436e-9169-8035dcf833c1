package faultNotification

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"unicode/utf8"

	"cloud.google.com/go/pubsub"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
)

// NotificationService defines the interface for processing fault notifications
type NotificationService interface {
	ProcessFaultNotification(ctx context.Context, faultData *FaultNotificationData) error
}

// NotificationPublisher defines the interface for publishing notifications
type NotificationPublisher interface {
	PublishNotification(ctx context.Context, notification *NotificationRequest) error
}

// notificationService implements NotificationService interface
type notificationService struct {
	userRepo       UserRepository
	publisher      NotificationPublisher
	websiteAppsURL string
}

// notificationPublisher implements NotificationPublisher interface
type notificationPublisher struct {
	pubsubClient connect.PsClient
	topicName    string
}

// NewNotificationService creates a new NotificationService instance
func NewNotificationService(userRepo UserRepository, publisher NotificationPublisher, websiteAppsURL string) NotificationService {
	return &notificationService{
		userRepo:       user<PERSON><PERSON><PERSON>,
		publisher:      publisher,
		websiteAppsURL: websiteAppsURL,
	}
}

// NewNotificationPublisher creates a new NotificationPublisher instance
func NewNotificationPublisher(pubsubClient connect.PsClient, topicName string) NotificationPublisher {
	return &notificationPublisher{
		pubsubClient: pubsubClient,
		topicName:    topicName,
	}
}

// ProcessFaultNotification processes fault notification for all eligible users
func (s *notificationService) ProcessFaultNotification(ctx context.Context, faultData *FaultNotificationData) error {
	logger.Debugf("Processing fault notification for device: %s", faultData.DeviceID)

	// 1. Lookup eligible users
	users, err := s.userRepo.GetEligibleUsers(ctx, faultData.DeviceID)
	if err != nil {
		logger.Errorf("Failed to get eligible users for device %s: %v", faultData.DeviceID, err)
		return fmt.Errorf("%w: %v", ErrUserLookup, err)
	}

	if len(users) == 0 {
		logger.Infof("No eligible users found for device %s", faultData.DeviceID)
		return nil // Not an error - just no notifications to send
	}

	logger.Infof("Found %d eligible users for device %s", len(users), faultData.DeviceID)

	// 2. Process notifications for each user
	var lastErr error
	successCount := 0

	for _, user := range users {
		// 3. Construct notification for this user
		notification := s.constructNotification(faultData, &user)

		// 4. Publish notification
		err := s.publisher.PublishNotification(ctx, notification)
		if err != nil {
			logger.Errorf("Failed to publish notification for user %d, device %s: %v", user.ID, faultData.DeviceID, err)
			lastErr = err
			continue
		}

		logger.Debugf("Successfully published notification for user %d, device %s", user.ID, faultData.DeviceID)
		successCount++
	}

	// 5. Handle results
	if successCount == 0 {
		logger.Errorf("Failed to send any notifications for device %s", faultData.DeviceID)
		return fmt.Errorf("%w: %v", ErrNotificationPublish, lastErr)
	}

	if successCount < len(users) {
		logger.Warnf("Sent %d/%d notifications for device %s", successCount, len(users), faultData.DeviceID)
	} else {
		logger.Infof("Successfully sent all %d notifications for device %s", successCount, faultData.DeviceID)
	}

	return nil
}

// constructNotification creates a notification request for a specific user
// Replicates the AWS Lambda message format exactly
func (s *notificationService) constructNotification(faultData *FaultNotificationData, user *User) *NotificationRequest {
	// Replicate AWS Lambda message format exactly:
	// "EDIFSA\n\nDevice:\nID: %s\nName: %s\n\nMsg:\n%s\n\nDetail:\n%s"
	message := fmt.Sprintf("EDIFSA\n\nDevice:\nID: %s\nName: %s\n\nMsg:\n%s\n\nDetail:\n%s",
		faultData.UserDeviceID,
		shortenString(faultData.UserDeviceName, 25),
		shortenString(faultData.FaultReason, 23),
		s.websiteAppsURL+faultData.DeviceID,
	)

	return &NotificationRequest{
		Type: "sms",
		Payload: map[string]interface{}{
			"to":      user.Mobile,
			"message": message,
		},
		Metadata: map[string]interface{}{
			"device_id":        faultData.DeviceID,
			"user_device_id":   faultData.UserDeviceID,
			"user_device_name": faultData.UserDeviceName,
			"fault_reason":     faultData.FaultReason,
			"faulted_at":       faultData.FaultedAt,
			"user_id":          user.ID,
			"user_timezone":    user.IANATimezone,
		},
	}
}

// PublishNotification publishes a notification to the notification-alerts-topic
func (p *notificationPublisher) PublishNotification(ctx context.Context, notification *NotificationRequest) error {
	logger.Debugf("Publishing notification to topic: %s", p.topicName)

	// Marshal notification to JSON
	data, err := json.Marshal(notification)
	if err != nil {
		logger.Errorf("Failed to marshal notification: %v", err)
		return fmt.Errorf("%w: failed to marshal notification: %v", ErrNotificationPublish, err)
	}

	// Publish to notification-alerts-topic
	topic := p.pubsubClient.Topic(p.topicName)
	result := topic.Publish(ctx, &pubsub.Message{
		Data: data,
		Attributes: map[string]string{
			"organizationIdentifier": getOrganizationIdentifier(),
			"topic":                  p.topicName,
		},
	})

	// Wait for publish result
	_, err = result.Get(ctx)
	if err != nil {
		logger.Errorf("Failed to publish to topic %s: %v", p.topicName, err)
		return fmt.Errorf("%w: %v", ErrNotificationPublish, err)
	}

	logger.Debugf("Successfully published notification to topic: %s", p.topicName)
	return nil
}

// shortenString truncates a string to maxChars length, preserving UTF-8 encoding
// Replicates the AWS Lambda implementation exactly
func shortenString(input string, maxChars int) string {
	// Return the input string if it's already within the limit
	if utf8.RuneCountInString(input) <= maxChars {
		return input
	}

	// Adjust maxChars to ensure it doesn't split in the middle of a UTF-8 encoded rune
	maxBytes := utf8.RuneCount([]byte(input))
	if maxBytes > maxChars {
		maxBytes = maxChars
	}

	// Return the shortened string with ellipsis if necessary
	shortened := string([]rune(input)[:maxBytes])
	if len(input) > maxChars {
		shortened += "..."
	}
	return shortened
}

// getOrganizationIdentifier returns the organization identifier for pub/sub attributes
// This would typically be extracted from the original message context
func getOrganizationIdentifier() string {
	// For now, use environment variable or default
	orgID := os.Getenv("ORGANIZATION_IDENTIFIER")
	if orgID == "" {
		orgID = "system" // Default fallback
	}
	return orgID
}
