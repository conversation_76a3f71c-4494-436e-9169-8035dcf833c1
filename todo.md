# Fault Notification Implementation TODO

## Project Overview
Implement fault notification functionality to replace AWS smsNotification lambda function with microservices architecture.

**Progress**: 6/17 tasks completed (~35%)

## Implementation Phases

### Phase 1: Core Components Setup
- [x] **Task 1.1**: Create models.go with data structures
  - [x] User struct
  - [x] FaultNotificationData struct
  - [x] NotificationRequest struct
  - [x] Build verification: `go build ./...`

- [x] **Task 1.2**: Create errors.go with error definitions
  - [x] Define all error variables
  - [x] Follow error naming conventions from coding rules
  - [x] Build verification: `go build ./...`

- [x] **Task 1.3**: Implement user_repository.go
  - [x] UserRepository interface
  - [x] userRepository struct implementation
  - [x] GetEligibleUsers method with proper SQL query
  - [x] Error handling and logging
  - [x] Build verification: `go build ./...`

- [x] **Task 1.4**: Write user_repository_test.go
  - [x] Test all methods with table-driven tests
  - [x] Mock database executor
  - [x] Test error scenarios
  - [x] Achieve 100% coverage
  - [x] Test verification: `go test -v -cover ./...`

### Phase 2: Business Logic Implementation
- [x] **Task 2.1**: Implement notification_service.go
  - [x] NotificationService interface
  - [x] notificationService struct
  - [x] ProcessFaultNotification method
  - [x] constructNotification method
  - [x] shortenString helper function
  - [x] Build verification: `go build ./...`

- [x] **Task 2.2**: Write notification_service_test.go
  - [x] Test ProcessFaultNotification with mocks
  - [x] Test constructNotification message format
  - [x] Test error scenarios and edge cases
  - [x] Achieve 100% coverage
  - [x] Test verification: `go test -v -cover ./...`

- [ ] **Task 2.3**: Implement publisher.go
  - [ ] NotificationPublisher interface
  - [ ] notificationPublisher struct
  - [ ] PublishNotification method
  - [ ] Proper JSON marshaling and Pub/Sub publishing
  - [ ] Build verification: `go build ./...`

- [ ] **Task 2.4**: Write publisher_test.go
  - [ ] Test PublishNotification with mock Pub/Sub client
  - [ ] Test JSON marshaling
  - [ ] Test error scenarios
  - [ ] Achieve 100% coverage
  - [ ] Test verification: `go test -v -cover ./...`

### Phase 3: Handler Integration
- [ ] **Task 3.1**: Extend HandlerDeps in handler.go
  - [ ] Add CreateNotificationService function
  - [ ] Add GetWebsiteAppsURL function
  - [ ] Build verification: `go build ./...`

- [ ] **Task 3.2**: Modify handler logic for notification processing
  - [ ] Add fault notification processing after BQ operations
  - [ ] Extract fault data properly
  - [ ] Handle errors gracefully without failing main processing
  - [ ] Build verification: `go build ./...`

- [ ] **Task 3.3**: Add environment configuration
  - [ ] Create getWebsiteAppsURL function
  - [ ] Handle WEBSITE_APPS environment variable
  - [ ] Build verification: `go build ./...`

- [ ] **Task 3.4**: Update handler_test.go
  - [ ] Extend existing tests to cover notification flow
  - [ ] Test with mocked notification service
  - [ ] Test error scenarios
  - [ ] Maintain 100% coverage
  - [ ] Test verification: `go test -v -cover ./...`

### Phase 4: Integration Testing
- [ ] **Task 4.1**: Create integration test structure
  - [ ] Create /workspace/microservices/testing/integration/fault_notification_test.go
  - [ ] Setup test environment
  - [ ] Build verification: `go build ./...`

- [ ] **Task 4.2**: Implement end-to-end integration tests
  - [ ] Test fault message publishing to gateway topic
  - [ ] Verify notification messages published to notification-alerts-sub
  - [ ] Test BigQuery persistence
  - [ ] Test with eligible and non-eligible users
  - [ ] Integration test verification: `go test -v ./microservices/testing/integration/...`

### Phase 5: Final Verification
- [ ] **Task 5.1**: Complete build verification
  - [ ] Run full build: `go build ./...`
  - [ ] Verify no compilation errors
  - [ ] Check all imports are correct

- [ ] **Task 5.2**: Complete test verification
  - [ ] Run all unit tests: `go test -v -cover ./microservices/etl/processors/handlers/gateway/faultNotification/...`
  - [ ] Verify 100% coverage for new code
  - [ ] Run integration tests: `go test -v ./microservices/testing/integration/...`
  - [ ] All tests must pass

- [ ] **Task 5.3**: Code quality verification
  - [ ] Verify coding standards compliance
  - [ ] Check error handling patterns
  - [ ] Verify logging implementation
  - [ ] Review dependency injection patterns

## Implementation Notes

### Build Commands
```bash
# Build verification after each task
go build ./microservices/etl/processors/handlers/gateway/faultNotification/

# Full project build
go build ./...

# Unit tests with coverage
go test -v -cover ./microservices/etl/processors/handlers/gateway/faultNotification/

# Integration tests
go test -v ./microservices/testing/integration/
```

### Success Criteria
- [ ] All tasks completed and marked as done
- [ ] All builds successful
- [ ] 100% unit test coverage for new code
- [ ] All integration tests passing
- [ ] Code follows established patterns and standards
- [ ] No breaking changes to existing functionality

### Dependencies
- Existing fault notification handler
- Database connection (UserDevice, User tables)
- Pub/Sub client (notification-alerts-sub topic)
- Environment variable: WEBSITE_APPS

### File Structure Created
```
/workspace/microservices/etl/processors/handlers/gateway/faultNotification/
├── models.go                     # NEW
├── errors.go                     # NEW  
├── user_repository.go            # NEW
├── user_repository_test.go       # NEW
├── notification_service.go       # NEW
├── notification_service_test.go  # NEW
├── publisher.go                  # NEW
├── publisher_test.go             # NEW
├── handler.go                    # MODIFIED
├── handler_test.go               # MODIFIED

/workspace/microservices/testing/integration/
├── fault_notification_test.go    # NEW
```

## Progress Tracking
- **Phase 1**: 0/4 tasks completed
- **Phase 2**: 0/4 tasks completed  
- **Phase 3**: 0/4 tasks completed
- **Phase 4**: 0/2 tasks completed
- **Phase 5**: 0/3 tasks completed

**Overall Progress**: 0/17 tasks completed (0%)
